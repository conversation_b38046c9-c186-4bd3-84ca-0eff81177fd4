#!/usr/bin/env python3
"""
Resume Builder Comprehensive Testing Suite

This script provides comprehensive testing for the FAAFO Career Platform Resume Builder
functionality using the Enhanced Testerat framework.

Features tested:
- Resume creation flow
- Personal information forms
- Experience section management
- Education section management
- Skills section management
- Template selection and preview
- Save and persistence functionality
- Resume list management
- Form validation and error handling
- Responsive design
- Security testing

Usage:
    python test_resume_builder.py
    python test_resume_builder.py --headless
    python test_resume_builder.py --verbose
"""

import sys
import time
import logging
from pathlib import Path
from typing import Dict, List, Any

# Add testerat_enhanced to path
sys.path.insert(0, str(Path(__file__).parent.parent.parent))

from testerat_enhanced import EnhancedTesterat, UniversalTestConfig, FrameworkType
from testerat_enhanced.utils.test_result import TestResult, TestCategory


class ResumeBuilderTester:
    """Specialized tester for Resume Builder functionality"""
    
    def __init__(self, config: UniversalTestConfig = None):
        self.config = config or UniversalTestConfig()
        self.logger = self._setup_logging()
        self.testerat = EnhancedTesterat(self.config)
        
        # Test data for resume builder
        self.test_data = {
            'personal_info': {
                'firstName': 'John',
                'lastName': 'Doe',
                'email': '<EMAIL>',
                'phone': '+****************',
                'location': 'San Francisco, CA',
                'website': 'https://johndoe.com',
                'linkedIn': 'https://linkedin.com/in/johndoe'
            },
            'summary': 'Experienced software engineer with 5+ years of experience in full-stack development, specializing in React, Node.js, and cloud technologies.',
            'experience': [
                {
                    'company': 'Tech Corp',
                    'position': 'Senior Software Engineer',
                    'startDate': '2020-01',
                    'endDate': '2023-12',
                    'description': 'Led development of web applications using React and Node.js',
                    'achievements': [
                        'Improved application performance by 40%',
                        'Led team of 5 developers'
                    ]
                },
                {
                    'company': 'Innovation Labs',
                    'position': 'Lead Software Engineer',
                    'startDate': '2024-01',
                    'endDate': '',  # Current position
                    'description': 'Leading development of next-generation web applications'
                }
            ],
            'education': [
                {
                    'institution': 'University of California, Berkeley',
                    'degree': 'Bachelor of Science',
                    'field': 'Computer Science',
                    'startDate': '2016-08',
                    'endDate': '2020-05',
                    'gpa': '3.8',
                    'honors': 'Magna Cum Laude'
                }
            ],
            'skills': [
                {'name': 'JavaScript', 'level': 'Expert', 'category': 'Programming Languages'},
                {'name': 'Python', 'level': 'Advanced', 'category': 'Programming Languages'},
                {'name': 'React', 'level': 'Expert', 'category': 'Frameworks'},
                {'name': 'Node.js', 'level': 'Advanced', 'category': 'Frameworks'},
                {'name': 'Git', 'level': 'Advanced', 'category': 'Tools'},
                {'name': 'Docker', 'level': 'Intermediate', 'category': 'Tools'}
            ]
        }
    
    def _setup_logging(self):
        """Setup logging for resume builder testing"""
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler('resume_builder_testing.log'),
                logging.StreamHandler()
            ]
        )
        return logging.getLogger(__name__)
    
    def run_comprehensive_resume_builder_tests(self, url: str) -> Dict[str, Any]:
        """
        Run comprehensive resume builder testing suite
        
        Test phases:
        1. Authentication and navigation
        2. Resume creation flow
        3. Form functionality testing
        4. Data persistence testing
        5. Template and preview testing
        6. Resume management testing
        7. Error handling and validation
        8. Security and edge cases
        """
        self.logger.info("🎯 Starting Resume Builder Comprehensive Testing")
        
        # Run base testerat tests first
        base_results = self.testerat.run_comprehensive_test(url, "Resume Builder Testing")
        
        # Add specialized resume builder tests
        specialized_results = self._run_specialized_resume_tests(url)
        
        # Combine results
        combined_results = self._combine_test_results(base_results, specialized_results)
        
        return combined_results
    
    def _run_specialized_resume_tests(self, url: str) -> List[TestResult]:
        """Run specialized resume builder tests"""
        results = []
        
        try:
            # Setup browser for specialized tests
            self.testerat._setup_browser()
            page = self.testerat.context.new_page()
            
            # Navigate to resume builder
            page.goto(url, wait_until='networkidle')
            
            # Test Phase 1: Authentication and Navigation
            results.extend(self._test_authentication_flow(page))
            
            # Test Phase 2: Resume Creation Flow
            results.extend(self._test_resume_creation_flow(page))
            
            # Test Phase 3: Form Functionality
            results.extend(self._test_form_functionality(page))
            
            # Test Phase 4: Data Persistence
            results.extend(self._test_data_persistence(page))
            
            # Test Phase 5: Template and Preview
            results.extend(self._test_template_and_preview(page))
            
            # Test Phase 6: Resume Management
            results.extend(self._test_resume_management(page))
            
            # Test Phase 7: Error Handling
            results.extend(self._test_error_handling(page))
            
            # Test Phase 8: Security and Edge Cases
            results.extend(self._test_security_and_edge_cases(page))
            
            page.close()
            self.testerat._cleanup_browser()
            
        except Exception as e:
            self.logger.error(f"Specialized testing failed: {e}")
            results.append(TestResult(
                test_name="specialized_testing_error",
                status="FAILED",
                details=f"Specialized testing failed: {str(e)}",
                severity="HIGH",
                recommendations=["Check resume builder accessibility", "Verify authentication system"],
                execution_time=0.0,
                category=TestCategory.WORKFLOW
            ))
        
        return results
    
    def _test_authentication_flow(self, page) -> List[TestResult]:
        """Test authentication and navigation to resume builder"""
        results = []
        start_time = time.time()
        
        try:
            # Test login navigation
            login_found = False
            login_selectors = [
                'a[href*="login"]',
                'a[href*="signin"]',
                'button:has-text("Login")',
                'button:has-text("Sign In")'
            ]
            
            for selector in login_selectors:
                try:
                    login_element = page.query_selector(selector)
                    if login_element:
                        login_found = True
                        break
                except:
                    continue
            
            if login_found:
                # Try to navigate to login
                try:
                    login_element.click()
                    page.wait_for_timeout(2000)
                    
                    # Check if login form is present
                    email_input = page.query_selector('input[type="email"], input[name*="email"]')
                    password_input = page.query_selector('input[type="password"]')
                    
                    if email_input and password_input:
                        # Test login with test credentials
                        email_input.fill('<EMAIL>')
                        password_input.fill('testpassword')
                        
                        # Find and click submit button
                        submit_btn = page.query_selector('button[type="submit"], input[type="submit"]')
                        if submit_btn:
                            submit_btn.click()
                            page.wait_for_timeout(3000)
                            
                            results.append(TestResult(
                                test_name="authentication_flow",
                                status="PASSED",
                                details="Successfully authenticated with test credentials",
                                severity="LOW",
                                recommendations=[],
                                execution_time=time.time() - start_time,
                                category=TestCategory.AUTHENTICATION
                            ))
                        else:
                            results.append(TestResult(
                                test_name="authentication_flow",
                                status="FAILED",
                                details="Login form missing submit button",
                                severity="MEDIUM",
                                recommendations=["Add submit button to login form"],
                                execution_time=time.time() - start_time,
                                category=TestCategory.AUTHENTICATION
                            ))
                    else:
                        results.append(TestResult(
                            test_name="authentication_flow",
                            status="FAILED",
                            details="Login form missing email or password fields",
                            severity="MEDIUM",
                            recommendations=["Ensure login form has proper input fields"],
                            execution_time=time.time() - start_time,
                            category=TestCategory.AUTHENTICATION
                        ))
                except Exception as e:
                    results.append(TestResult(
                        test_name="authentication_flow",
                        status="FAILED",
                        details=f"Login process failed: {str(e)}",
                        severity="HIGH",
                        recommendations=["Check authentication system functionality"],
                        execution_time=time.time() - start_time,
                        category=TestCategory.AUTHENTICATION
                    ))
            else:
                results.append(TestResult(
                    test_name="authentication_flow",
                    status="WARNING",
                    details="No login link found - testing without authentication",
                    severity="LOW",
                    recommendations=["Verify authentication is not required for resume builder"],
                    execution_time=time.time() - start_time,
                    category=TestCategory.AUTHENTICATION
                ))
            
        except Exception as e:
            results.append(TestResult(
                test_name="authentication_flow",
                status="ERROR",
                details=f"Authentication testing error: {str(e)}",
                severity="HIGH",
                recommendations=["Check page accessibility and authentication system"],
                execution_time=time.time() - start_time,
                category=TestCategory.AUTHENTICATION
            ))
        
        return results

    def _test_form_functionality(self, page) -> List[TestResult]:
        """Test form functionality with comprehensive input testing"""
        results = []
        start_time = time.time()

        try:
            # Test Personal Information Form
            personal_results = self._test_personal_info_form(page)
            results.extend(personal_results)

            # Test Experience Form
            experience_results = self._test_experience_form(page)
            results.extend(experience_results)

            # Test Education Form
            education_results = self._test_education_form(page)
            results.extend(education_results)

            # Test Skills Form
            skills_results = self._test_skills_form(page)
            results.extend(skills_results)

        except Exception as e:
            results.append(TestResult(
                test_name="form_functionality",
                status="ERROR",
                details=f"Form functionality testing error: {str(e)}",
                severity="HIGH",
                recommendations=["Check form accessibility and functionality"],
                execution_time=time.time() - start_time,
                category=TestCategory.WORKFLOW
            ))

        return results

    def _test_personal_info_form(self, page) -> List[TestResult]:
        """Test personal information form"""
        results = []
        start_time = time.time()

        try:
            # Look for personal info tab or section
            personal_tab = page.query_selector('button:has-text("Personal"), [role="tab"]:has-text("Personal")')
            if personal_tab:
                personal_tab.click()
                page.wait_for_timeout(1000)

            # Test form fields
            form_fields = {
                'firstName': self.test_data['personal_info']['firstName'],
                'lastName': self.test_data['personal_info']['lastName'],
                'email': self.test_data['personal_info']['email'],
                'phone': self.test_data['personal_info']['phone'],
                'location': self.test_data['personal_info']['location'],
                'website': self.test_data['personal_info']['website'],
                'linkedIn': self.test_data['personal_info']['linkedIn']
            }

            filled_fields = 0
            for field_name, field_value in form_fields.items():
                try:
                    # Try multiple selector patterns
                    selectors = [
                        f'input[name="{field_name}"]',
                        f'input[id="{field_name}"]',
                        f'input[placeholder*="{field_name}"]'
                    ]

                    field_element = None
                    for selector in selectors:
                        field_element = page.query_selector(selector)
                        if field_element:
                            break

                    if field_element:
                        field_element.fill(field_value)
                        page.wait_for_timeout(200)
                        filled_fields += 1
                except:
                    continue

            # Test professional summary
            summary_field = page.query_selector('textarea[placeholder*="summary"], textarea[name*="summary"]')
            if summary_field:
                summary_field.fill(self.test_data['summary'])
                filled_fields += 1

            if filled_fields >= 3:  # At least 3 fields filled successfully
                results.append(TestResult(
                    test_name="personal_info_form",
                    status="PASSED",
                    details=f"Successfully filled {filled_fields} personal info fields",
                    severity="LOW",
                    recommendations=[],
                    execution_time=time.time() - start_time,
                    category=TestCategory.WORKFLOW
                ))
            else:
                results.append(TestResult(
                    test_name="personal_info_form",
                    status="FAILED",
                    details=f"Only filled {filled_fields} personal info fields",
                    severity="MEDIUM",
                    recommendations=["Check form field accessibility and naming"],
                    execution_time=time.time() - start_time,
                    category=TestCategory.WORKFLOW
                ))

        except Exception as e:
            results.append(TestResult(
                test_name="personal_info_form",
                status="ERROR",
                details=f"Personal info form error: {str(e)}",
                severity="MEDIUM",
                recommendations=["Check personal info form functionality"],
                execution_time=time.time() - start_time,
                category=TestCategory.WORKFLOW
            ))

        return results

    def _test_experience_form(self, page) -> List[TestResult]:
        """Test experience form functionality"""
        results = []
        start_time = time.time()

        try:
            # Navigate to experience tab
            experience_tab = page.query_selector('button:has-text("Experience"), [role="tab"]:has-text("Experience")')
            if experience_tab:
                experience_tab.click()
                page.wait_for_timeout(1000)

            # Look for "Add Experience" button
            add_button = page.query_selector('button:has-text("Add Experience"), button:has-text("Add")')
            if add_button:
                add_button.click()
                page.wait_for_timeout(1000)

                # Fill experience form
                experience_data = self.test_data['experience'][0]
                experience_fields = {
                    'company': experience_data['company'],
                    'position': experience_data['position'],
                    'startDate': experience_data['startDate'],
                    'endDate': experience_data['endDate'],
                    'description': experience_data['description']
                }

                filled_fields = 0
                for field_name, field_value in experience_fields.items():
                    try:
                        selectors = [
                            f'input[name*="{field_name}"]',
                            f'input[placeholder*="{field_name}"]',
                            f'textarea[name*="{field_name}"]'
                        ]

                        field_element = None
                        for selector in selectors:
                            field_element = page.query_selector(selector)
                            if field_element:
                                break

                        if field_element:
                            field_element.fill(field_value)
                            page.wait_for_timeout(200)
                            filled_fields += 1
                    except:
                        continue

                if filled_fields >= 3:
                    results.append(TestResult(
                        test_name="experience_form",
                        status="PASSED",
                        details=f"Successfully filled {filled_fields} experience fields",
                        severity="LOW",
                        recommendations=[],
                        execution_time=time.time() - start_time,
                        category=TestCategory.WORKFLOW
                    ))
                else:
                    results.append(TestResult(
                        test_name="experience_form",
                        status="FAILED",
                        details=f"Only filled {filled_fields} experience fields",
                        severity="MEDIUM",
                        recommendations=["Check experience form field accessibility"],
                        execution_time=time.time() - start_time,
                        category=TestCategory.WORKFLOW
                    ))
            else:
                results.append(TestResult(
                    test_name="experience_form",
                    status="FAILED",
                    details="Add Experience button not found",
                    severity="MEDIUM",
                    recommendations=["Add 'Add Experience' button to interface"],
                    execution_time=time.time() - start_time,
                    category=TestCategory.WORKFLOW
                ))

        except Exception as e:
            results.append(TestResult(
                test_name="experience_form",
                status="ERROR",
                details=f"Experience form error: {str(e)}",
                severity="MEDIUM",
                recommendations=["Check experience form functionality"],
                execution_time=time.time() - start_time,
                category=TestCategory.WORKFLOW
            ))

        return results

    def _test_education_form(self, page) -> List[TestResult]:
        """Test education form functionality"""
        results = []
        start_time = time.time()

        try:
            # Navigate to education tab
            education_tab = page.query_selector('button:has-text("Education"), [role="tab"]:has-text("Education")')
            if education_tab:
                education_tab.click()
                page.wait_for_timeout(1000)

            # Look for "Add Education" button
            add_button = page.query_selector('button:has-text("Add Education"), button:has-text("Add")')
            if add_button:
                add_button.click()
                page.wait_for_timeout(1000)

                # Fill education form
                education_data = self.test_data['education'][0]
                education_fields = {
                    'institution': education_data['institution'],
                    'degree': education_data['degree'],
                    'field': education_data['field'],
                    'startDate': education_data['startDate'],
                    'endDate': education_data['endDate'],
                    'gpa': education_data['gpa']
                }

                filled_fields = 0
                for field_name, field_value in education_fields.items():
                    try:
                        selectors = [
                            f'input[name*="{field_name}"]',
                            f'input[placeholder*="{field_name}"]'
                        ]

                        field_element = None
                        for selector in selectors:
                            field_element = page.query_selector(selector)
                            if field_element:
                                break

                        if field_element:
                            field_element.fill(field_value)
                            page.wait_for_timeout(200)
                            filled_fields += 1
                    except:
                        continue

                if filled_fields >= 3:
                    results.append(TestResult(
                        test_name="education_form",
                        status="PASSED",
                        details=f"Successfully filled {filled_fields} education fields",
                        severity="LOW",
                        recommendations=[],
                        execution_time=time.time() - start_time,
                        category=TestCategory.WORKFLOW
                    ))
                else:
                    results.append(TestResult(
                        test_name="education_form",
                        status="FAILED",
                        details=f"Only filled {filled_fields} education fields",
                        severity="MEDIUM",
                        recommendations=["Check education form field accessibility"],
                        execution_time=time.time() - start_time,
                        category=TestCategory.WORKFLOW
                    ))
            else:
                results.append(TestResult(
                    test_name="education_form",
                    status="FAILED",
                    details="Add Education button not found",
                    severity="MEDIUM",
                    recommendations=["Add 'Add Education' button to interface"],
                    execution_time=time.time() - start_time,
                    category=TestCategory.WORKFLOW
                ))

        except Exception as e:
            results.append(TestResult(
                test_name="education_form",
                status="ERROR",
                details=f"Education form error: {str(e)}",
                severity="MEDIUM",
                recommendations=["Check education form functionality"],
                execution_time=time.time() - start_time,
                category=TestCategory.WORKFLOW
            ))

        return results

    def _test_skills_form(self, page) -> List[TestResult]:
        """Test skills form functionality"""
        results = []
        start_time = time.time()

        try:
            # Navigate to skills tab
            skills_tab = page.query_selector('button:has-text("Skills"), [role="tab"]:has-text("Skills")')
            if skills_tab:
                skills_tab.click()
                page.wait_for_timeout(1000)

            # Test adding skills
            skills_added = 0
            for skill_data in self.test_data['skills'][:3]:  # Test first 3 skills
                try:
                    # Look for skill name input
                    skill_input = page.query_selector('input[name*="skill"], input[placeholder*="skill"]')
                    if skill_input:
                        skill_input.fill(skill_data['name'])
                        page.wait_for_timeout(200)

                        # Look for level selector
                        level_selector = page.query_selector('select[name*="level"], select[name*="proficiency"]')
                        if level_selector:
                            level_selector.select_option(skill_data['level'])
                            page.wait_for_timeout(200)

                        # Look for add button
                        add_skill_button = page.query_selector('button:has-text("Add Skill"), button:has-text("Add")')
                        if add_skill_button:
                            add_skill_button.click()
                            page.wait_for_timeout(500)
                            skills_added += 1
                except:
                    continue

            if skills_added >= 2:
                results.append(TestResult(
                    test_name="skills_form",
                    status="PASSED",
                    details=f"Successfully added {skills_added} skills",
                    severity="LOW",
                    recommendations=[],
                    execution_time=time.time() - start_time,
                    category=TestCategory.WORKFLOW
                ))
            else:
                results.append(TestResult(
                    test_name="skills_form",
                    status="FAILED",
                    details=f"Only added {skills_added} skills",
                    severity="MEDIUM",
                    recommendations=["Check skills form functionality and interface"],
                    execution_time=time.time() - start_time,
                    category=TestCategory.WORKFLOW
                ))

        except Exception as e:
            results.append(TestResult(
                test_name="skills_form",
                status="ERROR",
                details=f"Skills form error: {str(e)}",
                severity="MEDIUM",
                recommendations=["Check skills form functionality"],
                execution_time=time.time() - start_time,
                category=TestCategory.WORKFLOW
            ))

        return results

    def _test_data_persistence(self, page) -> List[TestResult]:
        """Test data persistence and save functionality"""
        results = []
        start_time = time.time()

        try:
            # Look for save button
            save_button = page.query_selector('button:has-text("Save")')
            if save_button:
                save_button.click()
                page.wait_for_timeout(3000)

                # Check for success indicators
                success_indicators = [
                    '.success',
                    '.saved',
                    '[role="alert"]:has-text("saved")',
                    'text="saved"'
                ]

                save_success = any(page.query_selector(selector) for selector in success_indicators)

                if save_success:
                    results.append(TestResult(
                        test_name="data_persistence",
                        status="PASSED",
                        details="Resume saved successfully",
                        severity="LOW",
                        recommendations=[],
                        execution_time=time.time() - start_time,
                        category=TestCategory.WORKFLOW
                    ))
                else:
                    results.append(TestResult(
                        test_name="data_persistence",
                        status="WARNING",
                        details="Save button clicked but no success confirmation",
                        severity="MEDIUM",
                        recommendations=["Add save success feedback to user"],
                        execution_time=time.time() - start_time,
                        category=TestCategory.WORKFLOW
                    ))
            else:
                results.append(TestResult(
                    test_name="data_persistence",
                    status="FAILED",
                    details="Save button not found",
                    severity="HIGH",
                    recommendations=["Add save button to resume builder"],
                    execution_time=time.time() - start_time,
                    category=TestCategory.WORKFLOW
                ))

        except Exception as e:
            results.append(TestResult(
                test_name="data_persistence",
                status="ERROR",
                details=f"Data persistence error: {str(e)}",
                severity="MEDIUM",
                recommendations=["Check save functionality"],
                execution_time=time.time() - start_time,
                category=TestCategory.WORKFLOW
            ))

        return results

    def _test_template_and_preview(self, page) -> List[TestResult]:
        """Test template selection and preview functionality"""
        results = []
        start_time = time.time()

        try:
            # Look for preview button
            preview_button = page.query_selector('button:has-text("Preview")')
            if preview_button:
                preview_button.click()
                page.wait_for_timeout(2000)

                # Check if preview opened
                preview_indicators = [
                    '.preview',
                    '[role="dialog"]',
                    '.modal',
                    '.resume-preview'
                ]

                preview_opened = any(page.query_selector(selector) for selector in preview_indicators)

                if preview_opened:
                    results.append(TestResult(
                        test_name="template_and_preview",
                        status="PASSED",
                        details="Preview functionality working",
                        severity="LOW",
                        recommendations=[],
                        execution_time=time.time() - start_time,
                        category=TestCategory.WORKFLOW
                    ))
                else:
                    results.append(TestResult(
                        test_name="template_and_preview",
                        status="FAILED",
                        details="Preview did not open",
                        severity="MEDIUM",
                        recommendations=["Check preview functionality"],
                        execution_time=time.time() - start_time,
                        category=TestCategory.WORKFLOW
                    ))
            else:
                results.append(TestResult(
                    test_name="template_and_preview",
                    status="FAILED",
                    details="Preview button not found",
                    severity="MEDIUM",
                    recommendations=["Add preview button to resume builder"],
                    execution_time=time.time() - start_time,
                    category=TestCategory.WORKFLOW
                ))

        except Exception as e:
            results.append(TestResult(
                test_name="template_and_preview",
                status="ERROR",
                details=f"Template and preview error: {str(e)}",
                severity="MEDIUM",
                recommendations=["Check preview functionality"],
                execution_time=time.time() - start_time,
                category=TestCategory.WORKFLOW
            ))

        return results

    def _test_resume_management(self, page) -> List[TestResult]:
        """Test resume management functionality"""
        results = []
        start_time = time.time()

        try:
            # Try to navigate back to resume list
            back_button = page.query_selector('button:has-text("Cancel"), button:has-text("Back")')
            if back_button:
                back_button.click()
                page.wait_for_timeout(2000)

                # Check if resume list is visible
                list_indicators = [
                    '.resume-list',
                    'button:has-text("Create")',
                    'table',
                    '.resume-item'
                ]

                list_visible = any(page.query_selector(selector) for selector in list_indicators)

                if list_visible:
                    results.append(TestResult(
                        test_name="resume_management",
                        status="PASSED",
                        details="Resume list navigation working",
                        severity="LOW",
                        recommendations=[],
                        execution_time=time.time() - start_time,
                        category=TestCategory.WORKFLOW
                    ))
                else:
                    results.append(TestResult(
                        test_name="resume_management",
                        status="FAILED",
                        details="Resume list not accessible",
                        severity="MEDIUM",
                        recommendations=["Check resume list navigation"],
                        execution_time=time.time() - start_time,
                        category=TestCategory.WORKFLOW
                    ))
            else:
                results.append(TestResult(
                    test_name="resume_management",
                    status="WARNING",
                    details="No back/cancel button found",
                    severity="LOW",
                    recommendations=["Add navigation back to resume list"],
                    execution_time=time.time() - start_time,
                    category=TestCategory.WORKFLOW
                ))

        except Exception as e:
            results.append(TestResult(
                test_name="resume_management",
                status="ERROR",
                details=f"Resume management error: {str(e)}",
                severity="MEDIUM",
                recommendations=["Check resume management functionality"],
                execution_time=time.time() - start_time,
                category=TestCategory.WORKFLOW
            ))

        return results

    def _test_error_handling(self, page) -> List[TestResult]:
        """Test error handling and validation"""
        results = []
        start_time = time.time()

        try:
            # Test form validation by submitting empty required fields
            save_button = page.query_selector('button:has-text("Save")')
            if save_button:
                # Clear a required field and try to save
                email_field = page.query_selector('input[type="email"]')
                if email_field:
                    email_field.fill('')
                    save_button.click()
                    page.wait_for_timeout(1000)

                    # Check for validation errors
                    error_indicators = [
                        '.error',
                        '.invalid',
                        '[role="alert"]',
                        '.validation-error'
                    ]

                    validation_shown = any(page.query_selector(selector) for selector in error_indicators)

                    if validation_shown:
                        results.append(TestResult(
                            test_name="error_handling",
                            status="PASSED",
                            details="Form validation working properly",
                            severity="LOW",
                            recommendations=[],
                            execution_time=time.time() - start_time,
                            category=TestCategory.VALIDATION
                        ))
                    else:
                        results.append(TestResult(
                            test_name="error_handling",
                            status="FAILED",
                            details="No validation errors shown for invalid input",
                            severity="MEDIUM",
                            recommendations=["Add form validation feedback"],
                            execution_time=time.time() - start_time,
                            category=TestCategory.VALIDATION
                        ))
                else:
                    results.append(TestResult(
                        test_name="error_handling",
                        status="WARNING",
                        details="Could not test validation - no email field found",
                        severity="LOW",
                        recommendations=["Ensure required fields are properly marked"],
                        execution_time=time.time() - start_time,
                        category=TestCategory.VALIDATION
                    ))
            else:
                results.append(TestResult(
                    test_name="error_handling",
                    status="WARNING",
                    details="Could not test validation - no save button found",
                    severity="LOW",
                    recommendations=["Add save functionality to test validation"],
                    execution_time=time.time() - start_time,
                    category=TestCategory.VALIDATION
                ))

        except Exception as e:
            results.append(TestResult(
                test_name="error_handling",
                status="ERROR",
                details=f"Error handling test error: {str(e)}",
                severity="MEDIUM",
                recommendations=["Check error handling functionality"],
                execution_time=time.time() - start_time,
                category=TestCategory.VALIDATION
            ))

        return results

    def _test_security_and_edge_cases(self, page) -> List[TestResult]:
        """Test security and edge cases"""
        results = []
        start_time = time.time()

        try:
            # Test XSS prevention
            xss_payload = "<script>alert('xss')</script>"
            text_inputs = page.query_selector_all('input[type="text"], textarea')

            xss_protected = True
            for input_field in text_inputs[:3]:  # Test first 3 inputs
                try:
                    input_field.fill(xss_payload)
                    page.wait_for_timeout(500)

                    # Check if script executed (bad) or was sanitized (good)
                    page_content = page.content()
                    if xss_payload in page_content and '<script>' in page_content:
                        xss_protected = False
                        break
                except:
                    continue

            if xss_protected:
                results.append(TestResult(
                    test_name="security_xss_protection",
                    status="PASSED",
                    details="XSS protection working properly",
                    severity="LOW",
                    recommendations=[],
                    execution_time=time.time() - start_time,
                    category=TestCategory.SECURITY
                ))
            else:
                results.append(TestResult(
                    test_name="security_xss_protection",
                    status="FAILED",
                    details="XSS vulnerability detected",
                    severity="CRITICAL",
                    recommendations=["Implement input sanitization", "Add XSS protection"],
                    execution_time=time.time() - start_time,
                    category=TestCategory.SECURITY
                ))

        except Exception as e:
            results.append(TestResult(
                test_name="security_and_edge_cases",
                status="ERROR",
                details=f"Security testing error: {str(e)}",
                severity="MEDIUM",
                recommendations=["Check security testing functionality"],
                execution_time=time.time() - start_time,
                category=TestCategory.SECURITY
            ))

        return results

    def _combine_test_results(self, base_results: Dict[str, Any], specialized_results: List[TestResult]) -> Dict[str, Any]:
        """Combine base testerat results with specialized resume builder results"""
        # Add specialized results to the base results
        base_results['specialized_tests'] = [result.to_dict() for result in specialized_results]

        # Update summary
        specialized_passed = len([r for r in specialized_results if r.status == "PASSED"])
        specialized_failed = len([r for r in specialized_results if r.status in ["FAILED", "ERROR"]])
        specialized_total = len(specialized_results)

        base_results['summary']['specialized_tests'] = {
            'total': specialized_total,
            'passed': specialized_passed,
            'failed': specialized_failed,
            'success_rate': (specialized_passed / specialized_total * 100) if specialized_total > 0 else 0
        }

        # Add specialized critical issues
        specialized_critical = [r for r in specialized_results if r.severity in ["CRITICAL", "HIGH"]]
        base_results['specialized_critical_issues'] = [result.to_dict() for result in specialized_critical]

        return base_results


def main():
    """Main execution function"""
    import argparse

    parser = argparse.ArgumentParser(description="Resume Builder Comprehensive Testing")
    parser.add_argument('--url', default='http://localhost:3002', help='URL to test')
    parser.add_argument('--headless', action='store_true', help='Run in headless mode')
    parser.add_argument('--verbose', action='store_true', help='Enable verbose logging')

    args = parser.parse_args()

    # Create configuration
    config = UniversalTestConfig()
    config.headless = args.headless
    config.detailed_logging = args.verbose

    # Initialize tester
    tester = ResumeBuilderTester(config)

    print("🎯 Starting Resume Builder Comprehensive Testing")
    print(f"📍 Testing URL: {args.url}")
    print(f"🔧 Headless mode: {args.headless}")
    print("=" * 60)

    try:
        # Run comprehensive tests
        results = tester.run_comprehensive_resume_builder_tests(args.url)

        # Print summary
        print("\n📊 Test Results Summary:")
        print(f"Base Tests: {results['summary']['passed']}/{results['summary']['total']} passed")

        if 'specialized_tests' in results:
            spec_summary = results['summary']['specialized_tests']
            print(f"Resume Builder Tests: {spec_summary['passed']}/{spec_summary['total']} passed")
            print(f"Success Rate: {spec_summary['success_rate']:.1f}%")

        # Print critical issues
        if results.get('critical_issues'):
            print(f"\n⚠️ Critical Issues Found: {len(results['critical_issues'])}")
            for issue in results['critical_issues'][:3]:  # Show first 3
                print(f"  - {issue['test_name']}: {issue['details']}")

        if results.get('specialized_critical_issues'):
            print(f"\n🔍 Resume Builder Critical Issues: {len(results['specialized_critical_issues'])}")
            for issue in results['specialized_critical_issues'][:3]:
                print(f"  - {issue['test_name']}: {issue['details']}")

        print(f"\n📄 Reports generated: {len(results.get('report_files', []))}")
        for report_file in results.get('report_files', []):
            print(f"  - {report_file}")

        print("\n✅ Resume Builder testing completed!")

    except Exception as e:
        print(f"\n❌ Testing failed: {str(e)}")
        return 1

    return 0


if __name__ == "__main__":
    exit(main())
    
    def _test_resume_creation_flow(self, page) -> List[TestResult]:
        """Test resume creation flow"""
        results = []
        start_time = time.time()
        
        try:
            # Navigate to resume builder
            resume_builder_found = False
            resume_selectors = [
                'a[href*="resume-builder"]',
                'a[href*="resume"]',
                'button:has-text("Resume")',
                'button:has-text("Create Resume")'
            ]
            
            for selector in resume_selectors:
                try:
                    element = page.query_selector(selector)
                    if element:
                        element.click()
                        page.wait_for_timeout(2000)
                        resume_builder_found = True
                        break
                except:
                    continue
            
            if not resume_builder_found:
                # Try direct navigation
                try:
                    current_url = page.url
                    base_url = current_url.split('?')[0].rstrip('/')
                    page.goto(f"{base_url}/resume-builder", wait_until='networkidle')
                    resume_builder_found = True
                except:
                    pass
            
            if resume_builder_found:
                # Look for "Create New Resume" button
                create_button = page.query_selector('button:has-text("Create"), button:has-text("New")')
                if create_button:
                    create_button.click()
                    page.wait_for_timeout(2000)
                    
                    # Check if resume builder interface loaded
                    builder_indicators = [
                        'input[placeholder*="resume"]',
                        'input[placeholder*="title"]',
                        'form',
                        '[role="tablist"]',
                        'button:has-text("Save")'
                    ]
                    
                    builder_loaded = any(page.query_selector(selector) for selector in builder_indicators)
                    
                    if builder_loaded:
                        results.append(TestResult(
                            test_name="resume_creation_flow",
                            status="PASSED",
                            details="Resume builder interface loaded successfully",
                            severity="LOW",
                            recommendations=[],
                            execution_time=time.time() - start_time,
                            category=TestCategory.WORKFLOW
                        ))
                    else:
                        results.append(TestResult(
                            test_name="resume_creation_flow",
                            status="FAILED",
                            details="Resume builder interface did not load properly",
                            severity="HIGH",
                            recommendations=["Check resume builder component loading"],
                            execution_time=time.time() - start_time,
                            category=TestCategory.WORKFLOW
                        ))
                else:
                    results.append(TestResult(
                        test_name="resume_creation_flow",
                        status="FAILED",
                        details="Create new resume button not found",
                        severity="MEDIUM",
                        recommendations=["Add create new resume button to interface"],
                        execution_time=time.time() - start_time,
                        category=TestCategory.WORKFLOW
                    ))
            else:
                results.append(TestResult(
                    test_name="resume_creation_flow",
                    status="FAILED",
                    details="Could not navigate to resume builder",
                    severity="HIGH",
                    recommendations=["Check resume builder navigation and routing"],
                    execution_time=time.time() - start_time,
                    category=TestCategory.WORKFLOW
                ))
                
        except Exception as e:
            results.append(TestResult(
                test_name="resume_creation_flow",
                status="ERROR",
                details=f"Resume creation flow error: {str(e)}",
                severity="HIGH",
                recommendations=["Check resume builder accessibility and functionality"],
                execution_time=time.time() - start_time,
                category=TestCategory.WORKFLOW
            ))
        
        return results
