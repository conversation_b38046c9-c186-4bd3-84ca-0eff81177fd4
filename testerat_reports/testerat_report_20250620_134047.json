{"metadata": {"report_id": "20250620_134047", "generated_at": "2025-06-20T13:40:47.421090", "testerat_version": "2.0.0", "framework_info": {"framework": "FrameworkType.NEXTJS", "app_name": "FAAFO Career Platform - Find Your Path to Career Freedom", "version": null, "features": ["interactive-elements", "navigation", "accessibility-labels", "aria-roles"], "confidence": 1.0, "detection_methods": ["DOM analysis"], "detected_patterns": ["_next/static", "next/"]}, "app_info": {"name": "FAAFO Career Platform - Find Your Path to Career Freedom", "url": "http://localhost:3002"}}, "summary": {"suite_name": "Enhanced Testerat - http://localhost:3002", "total_tests": 12, "passed": 4, "failed": 7, "critical_issues": 0, "success_rate": 33.33333333333333, "total_execution_time": 14.785832166671753, "start_time": "2025-06-20T13:40:47.421332", "end_time": "2025-06-20T13:41:02.207157"}, "test_results": [{"test_name": "auth_state_consistency", "status": "FAILED", "details": "Logged-out content visible to authenticated user; No user-specific content found for authenticated user", "severity": "HIGH", "recommendations": ["Ensure authentication state is properly checked before rendering content", "Ensure authenticated users see personalized content"], "execution_time": 0.0, "screenshot_path": null, "category": "general", "timestamp": "2025-06-20T13:40:52.296408", "framework_detected": "nextjs", "auth_method_detected": null, "error_details": null, "code_location": null, "fix_examples": [], "performance_metrics": {}, "security_issues": [], "vulnerability_type": null}, {"test_name": "protected_route_access", "status": "FAILED", "details": "Tested 1 protected routes; Authenticated user redirected from protected route: /dashboard", "severity": "HIGH", "recommendations": ["Fix access control for /dashboard"], "execution_time": 0.0, "screenshot_path": null, "category": "general", "timestamp": "2025-06-20T13:40:53.968981", "framework_detected": "nextjs", "auth_method_detected": null, "error_details": null, "code_location": null, "fix_examples": [], "performance_metrics": {}, "security_issues": [], "vulnerability_type": null}, {"test_name": "session_management", "status": "FAILED", "details": "Session not persisted across page reload; CSRF token not found", "severity": "HIGH", "recommendations": ["Implement proper session persistence", "Implement CSRF protection"], "execution_time": 0.0, "screenshot_path": null, "category": "general", "timestamp": "2025-06-20T13:40:54.962277", "framework_detected": "nextjs", "auth_method_detected": null, "error_details": null, "code_location": null, "fix_examples": [], "performance_metrics": {}, "security_issues": [], "vulnerability_type": null}, {"test_name": "logout_flow", "status": "FAILED", "details": "Logout button not found", "severity": "MEDIUM", "recommendations": ["Ensure logout functionality is accessible"], "execution_time": 0.0, "screenshot_path": null, "category": "general", "timestamp": "2025-06-20T13:40:54.964487", "framework_detected": "nextjs", "auth_method_detected": null, "error_details": null, "code_location": null, "fix_examples": [], "performance_metrics": {}, "security_issues": [], "vulnerability_type": null}, {"test_name": "auth_edge_cases", "status": "PASSED", "details": "Authentication edge cases handled properly", "severity": "LOW", "recommendations": [], "execution_time": 0.0, "screenshot_path": null, "category": "general", "timestamp": "2025-06-20T13:40:56.358173", "framework_detected": "nextjs", "auth_method_detected": null, "error_details": null, "code_location": null, "fix_examples": [], "performance_metrics": {}, "security_issues": [], "vulnerability_type": null}, {"test_name": "workflow_detection", "status": "SKIPPED", "details": "No multi-step workflow detected", "severity": "LOW", "recommendations": ["Add workflow testing if multi-step processes exist"], "execution_time": 0.0, "screenshot_path": null, "category": "workflow", "timestamp": "2025-06-20T13:40:56.415593", "framework_detected": "nextjs", "auth_method_detected": null, "error_details": null, "code_location": null, "fix_examples": [], "performance_metrics": {}, "security_issues": [], "vulnerability_type": null}, {"test_name": "csrf_protection", "status": "PASSED", "details": "CSRF protection working properly", "severity": "LOW", "recommendations": [], "execution_time": 0.0, "screenshot_path": null, "category": "api", "timestamp": "2025-06-20T13:40:59.537618", "framework_detected": "nextjs", "auth_method_detected": null, "error_details": null, "code_location": null, "fix_examples": [], "performance_metrics": {}, "security_issues": [], "vulnerability_type": null}, {"test_name": "form_submissions", "status": "FAILED", "details": "Form 1 submission not captured", "severity": "HIGH", "recommendations": ["Fix form submission issues in form 1"], "execution_time": 0.0, "screenshot_path": null, "category": "api", "timestamp": "2025-06-20T13:41:01.622847", "framework_detected": "nextjs", "auth_method_detected": null, "error_details": null, "code_location": null, "fix_examples": [], "performance_metrics": {}, "security_issues": [], "vulnerability_type": null}, {"test_name": "api_endpoints", "status": "PASSED", "details": "API endpoints working properly", "severity": "LOW", "recommendations": [], "execution_time": 0.0, "screenshot_path": null, "category": "api", "timestamp": "2025-06-20T13:41:02.016187", "framework_detected": "nextjs", "auth_method_detected": null, "error_details": null, "code_location": null, "fix_examples": [], "performance_metrics": {}, "security_issues": [], "vulnerability_type": null}, {"test_name": "network_requests", "status": "FAILED", "details": "Failed request: POST http://localhost:3002/api/auth/callback/credentials - 401", "severity": "MEDIUM", "recommendations": ["Fix API error: 401"], "execution_time": 0.0, "screenshot_path": null, "category": "api", "timestamp": "2025-06-20T13:41:02.016334", "framework_detected": "nextjs", "auth_method_detected": null, "error_details": null, "code_location": null, "fix_examples": [], "performance_metrics": {}, "security_issues": [], "vulnerability_type": null}, {"test_name": "api_error_handling", "status": "PASSED", "details": "API error handling working properly", "severity": "LOW", "recommendations": [], "execution_time": 0.0, "screenshot_path": null, "category": "api", "timestamp": "2025-06-20T13:41:02.029193", "framework_detected": "nextjs", "auth_method_detected": null, "error_details": null, "code_location": null, "fix_examples": [], "performance_metrics": {}, "security_issues": [], "vulnerability_type": null}, {"test_name": "api_security", "status": "FAILED", "details": "Sensitive data in request body: http://localhost:3002/api/auth/callback/credentials", "severity": "HIGH", "recommendations": ["Encrypt or remove sensitive data from API requests"], "execution_time": 0.0, "screenshot_path": null, "category": "api", "timestamp": "2025-06-20T13:41:02.029484", "framework_detected": "nextjs", "auth_method_detected": null, "error_details": null, "code_location": null, "fix_examples": [], "performance_metrics": {}, "security_issues": [], "vulnerability_type": null}], "critical_issues": [], "failures": [{"test_name": "auth_state_consistency", "status": "FAILED", "details": "Logged-out content visible to authenticated user; No user-specific content found for authenticated user", "severity": "HIGH", "recommendations": ["Ensure authentication state is properly checked before rendering content", "Ensure authenticated users see personalized content"], "execution_time": 0.0, "screenshot_path": null, "category": "general", "timestamp": "2025-06-20T13:40:52.296408", "framework_detected": "nextjs", "auth_method_detected": null, "error_details": null, "code_location": null, "fix_examples": [], "performance_metrics": {}, "security_issues": [], "vulnerability_type": null}, {"test_name": "protected_route_access", "status": "FAILED", "details": "Tested 1 protected routes; Authenticated user redirected from protected route: /dashboard", "severity": "HIGH", "recommendations": ["Fix access control for /dashboard"], "execution_time": 0.0, "screenshot_path": null, "category": "general", "timestamp": "2025-06-20T13:40:53.968981", "framework_detected": "nextjs", "auth_method_detected": null, "error_details": null, "code_location": null, "fix_examples": [], "performance_metrics": {}, "security_issues": [], "vulnerability_type": null}, {"test_name": "session_management", "status": "FAILED", "details": "Session not persisted across page reload; CSRF token not found", "severity": "HIGH", "recommendations": ["Implement proper session persistence", "Implement CSRF protection"], "execution_time": 0.0, "screenshot_path": null, "category": "general", "timestamp": "2025-06-20T13:40:54.962277", "framework_detected": "nextjs", "auth_method_detected": null, "error_details": null, "code_location": null, "fix_examples": [], "performance_metrics": {}, "security_issues": [], "vulnerability_type": null}, {"test_name": "logout_flow", "status": "FAILED", "details": "Logout button not found", "severity": "MEDIUM", "recommendations": ["Ensure logout functionality is accessible"], "execution_time": 0.0, "screenshot_path": null, "category": "general", "timestamp": "2025-06-20T13:40:54.964487", "framework_detected": "nextjs", "auth_method_detected": null, "error_details": null, "code_location": null, "fix_examples": [], "performance_metrics": {}, "security_issues": [], "vulnerability_type": null}, {"test_name": "form_submissions", "status": "FAILED", "details": "Form 1 submission not captured", "severity": "HIGH", "recommendations": ["Fix form submission issues in form 1"], "execution_time": 0.0, "screenshot_path": null, "category": "api", "timestamp": "2025-06-20T13:41:01.622847", "framework_detected": "nextjs", "auth_method_detected": null, "error_details": null, "code_location": null, "fix_examples": [], "performance_metrics": {}, "security_issues": [], "vulnerability_type": null}, {"test_name": "network_requests", "status": "FAILED", "details": "Failed request: POST http://localhost:3002/api/auth/callback/credentials - 401", "severity": "MEDIUM", "recommendations": ["Fix API error: 401"], "execution_time": 0.0, "screenshot_path": null, "category": "api", "timestamp": "2025-06-20T13:41:02.016334", "framework_detected": "nextjs", "auth_method_detected": null, "error_details": null, "code_location": null, "fix_examples": [], "performance_metrics": {}, "security_issues": [], "vulnerability_type": null}, {"test_name": "api_security", "status": "FAILED", "details": "Sensitive data in request body: http://localhost:3002/api/auth/callback/credentials", "severity": "HIGH", "recommendations": ["Encrypt or remove sensitive data from API requests"], "execution_time": 0.0, "screenshot_path": null, "category": "api", "timestamp": "2025-06-20T13:41:02.029484", "framework_detected": "nextjs", "auth_method_detected": null, "error_details": null, "code_location": null, "fix_examples": [], "performance_metrics": {}, "security_issues": [], "vulnerability_type": null}], "recommendations": ["Fix form submission issues in form 1", "Ensure authentication state is properly checked before rendering content", "Implement proper session persistence", "Ensure logout functionality is accessible", "Ensure authenticated users see personalized content", "Implement CSRF protection", "Add workflow testing if multi-step processes exist", "Fix access control for /dashboard", "Encrypt or remove sensitive data from API requests", "Fix API error: 401"], "fix_examples": [], "code_locations": [], "statistics": {"categories": {"general": {"total": 5, "passed": 1, "failed": 4}, "workflow": {"total": 1, "passed": 1, "failed": 0}, "api": {"total": 6, "passed": 3, "failed": 3}}, "severities": {"HIGH": 5, "MEDIUM": 2, "LOW": 5}, "avg_execution_time": 0.0, "slowest_test": "auth_state_consistency", "fastest_test": "auth_state_consistency"}}